* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-top {
  background: rgba(0, 0, 0, 0.1);
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.search-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.search-input {
  padding: 10px 15px;
  border: none;
  border-radius: 25px 0 0 25px;
  outline: none;
  width: 300px;
  font-size: 1rem;
  background: white;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 0 25px 25px 0;
  background: #0d0000;
  color: white;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-btn:hover {
  background: #691B1B;
}

.search-btn i {
  font-size: 1rem;
}

.hero {
  background: linear-gradient(135deg, #691B1B, #0d0000);
  color: white;
  padding: 0 0 60px 0;
  text-align: center;
}

.hero .container {
  padding-top: 40px;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.subtitle {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
  text-align: center;
}

/* Nav */
nav ul {
  list-style: none;
  display: flex;
  justify-content: center;
  gap: 30px;
}

nav a {
  color: white;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 25px;
  transition: all 0.3s ease;
}

nav a:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.content-wrapper {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  margin: 40px 0;
  
}

.main-content {
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

section {
  margin-bottom: 50px;
}

section h2 {
  font-size: 2.2rem;
  color: #691B1B;
  margin-bottom: 20px;
  border-bottom: 3px solid #0d0000;
  padding-bottom: 10px;
}

.article-image {
  width: 100%;
  max-height: 400px;
  object-fit: cover;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

p {
  font-size: 1.1rem;
  margin-bottom: 15px;
  text-align: justify;
}

.tourism-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.tourism-card {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.tourism-card:hover {
  transform: translateY(-5px);
}

.tourism-card h3 {
  color: #691B1B;
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.tourism-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 15px;
}


.sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.city-info-card {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: sticky;
  top: 20px;
}

.city-info-card h3 {
  color: #691B1B;
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.tagline {
  font-style: italic;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.city-info-card img {
  width: 120px;
  height: 120px;
  object-fit: contain;
  margin-bottom: 10px;
}

figcaption {
  font-size: 0.9rem;
  color: #666;
}

.social-media-card {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-media-card h3 {
  color: #691B1B;
  font-size: 1.2rem;
  margin-bottom: 15px;
  text-align: center;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  text-decoration: none;
  color: white;
  transition: all 0.3s ease;
}

.social-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.social-link.facebook {
  background: #691B1B;
}

.social-link.twitter {
  background: #691B1B;
}

.social-link.instagram {
  background: #691B1B;
}

.social-link.youtube {
  background: #691B1B;
}

.social-link i {
  font-size: 1rem;
}

footer {
  background: linear-gradient(135deg, #691B1B, #0d0000);
  color: white;
  text-align: center;
  padding: 30px 0;
  margin-top: 60px;
}

/* Respon */
@media (max-width: 768px) {
  .content-wrapper {
    grid-template-columns: 1fr;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  nav ul {
    flex-direction: column;
    gap: 10px;
  }
  
  .main-content {
    padding: 20px;
  }
  
  .tourism-grid {
    grid-template-columns: 1fr;
  }
}
